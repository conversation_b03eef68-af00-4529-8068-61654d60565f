html, body {
    height: 100%;
    width: 100%;
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: 'Press Start 2P', cursive;
}
.background {
	height: 100vh;
	width: 100vw;
	background: url('images/bg\ pic-01.png') no-repeat center center fixed;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
}
.bird {
	height: 80px;
	width: 80px;
	position: fixed;
	top: 40vh;
	left: 30vw;
	z-index: 100;
}
.pipe_sprite {
	position: fixed;
	top: 20vh; 
	left: 60vw;
	height: 70vh;
	width: 6vw;
	background:radial-gradient(lightgreen 50%, green);
	border: 5px solid black;
}
.message {
	position: absolute;
	z-index: 10;
	color: black;
	top: 30%;
	left: 50%;
	font-size: 2.5em; /* Increased from 2em to 2.5em */
	transform: translate(-50%, -50%);
	text-align: center;
}
.messageStyle{
	background: white;
	padding: 30px;
	box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
	border-radius: 5%;
}
.score {
	position: fixed;
	z-index: 10;
	height: 10vh;
	font-size: 8vh; /* Reduced from 10vh to 8vh */
	font-weight: 100;
	color: white;
	-webkit-text-stroke-width: 2px;
    -webkit-text-stroke-color: black;
	top: 0;
	left: 0;
	margin: 10px;
	font-family: 'Press Start 2P', cursive;
}
.score_val {
	color: gold;
	font-weight: bold;
}
/* Victory Popup Styles */
.victory-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.victory-popup {
    background: white;
    border: 3px solid #333;
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    font-family: 'Press Start 2P', cursive;
}

.victory-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.victory-text {
    font-size: 16px;
    color: #333;
    line-height: 1.4;
}

.reward-text {
    font-size: 14px;
    color: #333;
    line-height: 1.4;
}

.trophy-container {
    margin: 10px 0;
}

.trophy-box {
    display: inline-block;
    background: #fff;
    border: 4px solid #FFD700;
    border-radius: 10px;
    padding: 10px;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    overflow: hidden;
}

.reward-gif {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 6px;
}

.victory-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.victory-btn {
    font-family: 'Press Start 2P', cursive;
    font-size: 12px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.continue-btn {
    background: #4CAF50;
    color: white;
}

.continue-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.exit-btn {
    background: #f44336;
    color: white;
}

.exit-btn:hover {
    background: #da190b;
    transform: translateY(-2px);
}

@media only screen and (max-width: 1080px) {
    .message{
		font-size: 50px;
		top: 50%;
		white-space: nowrap;
	}
	.score{
		font-size: 8vh;
	}
	.bird{
		width: 60px;
		height: 40px;
	}
	.pipe_sprite{
		width: 4vw;
	}

	.victory-popup {
        padding: 20px;
        margin: 10px;
    }

    .victory-text {
        font-size: 12px;
    }

    .reward-text {
        font-size: 10px;
    }

    .trophy-box {
        width: 100px;
        height: 100px;
        padding: 8px;
    }

    .victory-btn {
        font-size: 10px;
        padding: 10px 12px;
        min-width: 80px;
        flex: 1;
        max-width: 120px;
    }

    .victory-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }
}

/* Chapter Loading Screen Styles */
.chapter-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    overflow: hidden;
}

.loading-background {
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #1a1a2e 0%, #16213e 30%, #0f3460 60%, #533483 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Stars */
.stars-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.star {
    position: absolute;
    color: #ffd700;
    font-size: 24px;
    animation: twinkle 2s infinite alternate;
}

.star-1 {
    top: 10%;
    left: 15%;
    animation-delay: 0s;
}

.star-2 {
    top: 20%;
    right: 20%;
    animation-delay: 0.5s;
}

.star-3 {
    top: 15%;
    left: 50%;
    animation-delay: 1s;
}

.star-4 {
    top: 25%;
    right: 10%;
    animation-delay: 1.5s;
}

.star-5 {
    top: 30%;
    left: 80%;
    animation-delay: 2s;
}

@keyframes twinkle {
    0% { opacity: 0.3; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1.2); }
}

/* Clouds */
.clouds-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.cloud {
    position: absolute;
    background: #8B7EC8;
    border-radius: 50px;
    opacity: 0.8;
}

.cloud::before,
.cloud::after {
    content: '';
    position: absolute;
    background: #8B7EC8;
    border-radius: 50px;
}

.cloud-1 {
    width: 100px;
    height: 40px;
    top: 20%;
    left: 10%;
    animation: float 6s ease-in-out infinite;
}

.cloud-1::before {
    width: 50px;
    height: 50px;
    top: -25px;
    left: 10px;
}

.cloud-1::after {
    width: 60px;
    height: 40px;
    top: -15px;
    right: 10px;
}

.cloud-2 {
    width: 80px;
    height: 30px;
    top: 35%;
    right: 15%;
    animation: float 8s ease-in-out infinite reverse;
}

.cloud-2::before {
    width: 40px;
    height: 40px;
    top: -20px;
    left: 15px;
}

.cloud-2::after {
    width: 50px;
    height: 30px;
    top: -10px;
    right: 15px;
}

.cloud-3 {
    width: 120px;
    height: 50px;
    top: 10%;
    right: 30%;
    animation: float 10s ease-in-out infinite;
}

.cloud-3::before {
    width: 60px;
    height: 60px;
    top: -30px;
    left: 20px;
}

.cloud-3::after {
    width: 70px;
    height: 50px;
    top: -20px;
    right: 20px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Mountains */
.mountains-container {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 40%;
}

.mountain {
    position: absolute;
    bottom: 0;
}

.mountain-1 {
    width: 0;
    height: 0;
    border-left: 150px solid transparent;
    border-right: 150px solid transparent;
    border-bottom: 200px solid #2d5016;
    left: 0;
}

.mountain-2 {
    width: 0;
    height: 0;
    border-left: 120px solid transparent;
    border-right: 120px solid transparent;
    border-bottom: 160px solid #1a3d0a;
    left: 200px;
}

.mountain-3 {
    width: 0;
    height: 0;
    border-left: 180px solid transparent;
    border-right: 180px solid transparent;
    border-bottom: 220px solid #0f2905;
    right: 0;
}

/* Loading Content */
.loading-content {
    text-align: center;
    color: white;
    font-family: 'Press Start 2P', cursive;
    z-index: 10;
    position: relative;
}

.loading-title {
    font-size: 32px;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: glow 2s ease-in-out infinite alternate;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

.loading-subtitle {
    font-size: 20px;
    margin: 20px 0 10px 0;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.loading-description {
    font-size: 14px;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes glow {
    0% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 10px rgba(255, 255, 255, 0.3); }
    100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 20px rgba(255, 255, 255, 0.6); }
}

/* Responsive styles for loading screen */
@media only screen and (max-width: 1080px) {
    .loading-title {
        font-size: 24px;
        margin-bottom: 20px;
    }

    .loading-subtitle {
        font-size: 16px;
        margin: 15px 0 8px 0;
    }

    .loading-description {
        font-size: 12px;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        margin: 15px auto;
    }

    .star {
        font-size: 20px;
    }

    .cloud-1, .cloud-2, .cloud-3 {
        transform: scale(0.8);
    }

    .mountain-1 {
        border-left-width: 120px;
        border-right-width: 120px;
        border-bottom-width: 160px;
    }

    .mountain-2 {
        border-left-width: 100px;
        border-right-width: 100px;
        border-bottom-width: 130px;
        left: 160px;
    }

    .mountain-3 {
        border-left-width: 140px;
        border-right-width: 140px;
        border-bottom-width: 180px;
    }
}

@media only screen and (max-width: 768px) {
    .loading-title {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .loading-subtitle {
        font-size: 14px;
        margin: 12px 0 6px 0;
    }

    .loading-description {
        font-size: 10px;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        margin: 12px auto;
    }

    .star {
        font-size: 16px;
    }

    .cloud-1, .cloud-2, .cloud-3 {
        transform: scale(0.6);
    }

    .mountain-1 {
        border-left-width: 80px;
        border-right-width: 80px;
        border-bottom-width: 120px;
    }

    .mountain-2 {
        border-left-width: 70px;
        border-right-width: 70px;
        border-bottom-width: 100px;
        left: 120px;
    }

    .mountain-3 {
        border-left-width: 90px;
        border-right-width: 90px;
        border-bottom-width: 140px;
    }
}




