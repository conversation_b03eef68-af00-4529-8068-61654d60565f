body {
    margin: 0;
    overflow: hidden;
    font-family: 'Arial', sans-serif;
    background-color: #000;
    position: relative;
}

/* Back button styling */
.back-button {
    position: absolute;
    top: 20px;
    left: 20px;
    background-color: #00bfff;
    color: black;
    border: 3px solid #000;
    padding: 12px 20px;
    font-family: 'Press Start 2P', cursive;
    font-size: 0.8rem;
    cursor: pointer;
    border-radius: 25px;
    transition: all 0.2s ease;
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.5);
    box-shadow:
        inset 2px 2px 0 rgba(255, 255, 255, 0.3),
        inset -2px -2px 0 rgba(0, 0, 0, 0.3),
        0 4px 8px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    letter-spacing: 1px;
}

.back-button:hover {
    background-color: #1e90ff;
    transform: translateY(-2px);
    box-shadow:
        inset 2px 2px 0 rgba(255, 255, 255, 0.4),
        inset -2px -2px 0 rgba(0, 0, 0, 0.4),
        0 6px 12px rgba(0, 0, 0, 0.4);
}

.back-button:active {
    transform: translateY(0px);
    box-shadow:
        inset 2px 2px 0 rgba(255, 255, 255, 0.2),
        inset -2px -2px 0 rgba(0, 0, 0, 0.5),
        0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Black screen overlay for smooth transition */
#black-screen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    z-index: 10000;
    opacity: 1;
    transition: opacity 2s ease-out;
    pointer-events: none;
}

#black-screen-overlay.fade-out {
    opacity: 0;
}

canvas {
    display: block;
    margin: 0 auto;
    background-color: #222;
}

#message {
    position: absolute;
    top: 20%;
    left: 50%;
    background-image: url('./Layer_1.png');
    height: 210px;
    width: 450px;
    transform: translate(-50%, -50%);
    font-family: 'Press Start 2P', cursive;
    /* background-color: rgba(0, 0, 0, 0.85); */
    color: black;
    /* padding: 25px; */
    /* border-radius: 15px; */
    /* border: 3px solid #daa520; */
    text-align: center;
    font-size: 15px;
    display: none;
    z-index: 90;
    /* box-shadow: 0 0 20px rgba(218, 165, 32, 0.5); */
    max-width: 80%;
}

#close-message {
    margin-top: 40px;
    top: 20%;
    padding: 10px 25px;
    background-color: #2E7D32;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    font-size: 12px;
    font-family: 'Press Start 2P', cursive;
    transition: all 0.3s;
    text-transform: uppercase;
}

#close-message:hover {
    background-color: #4CAF50;
    transform: scale(1.05);
}